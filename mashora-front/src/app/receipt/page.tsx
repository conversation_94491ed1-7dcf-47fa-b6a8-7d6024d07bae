"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import {
  Download,
  ArrowRight,
  CreditCard,
  Calendar,
  Hash,
  DollarSign,
  CheckCircle,
  FileText,
  Printer,
  Share2,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

// Mock payment data
const mockPayment = {
  transactionId: "TXN-2024-001234",
  amount: 200,
  currency: "EGP",
  date: "2024-01-15",
  time: "14:30:25",
  paymentMethod: "بطاقة ائتمان",
  cardLast4: "1234",
  status: "مكتمل",
  courseName: "كورس الإعداد للزواج",
  studentName: "أبانوب نشأت",
  email: "<EMAIL>",
  phone: "01012345678",
  receiptNumber: "RCP-2024-001234",
}

export default function ReceiptPage() {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownloadPDF = async () => {
    setIsDownloading(true)
    try {
      // Simulate PDF generation and download
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // In a real app, this would generate and download the actual PDF
      const link = document.createElement("a")
      link.href = "#" // This would be the actual PDF URL
      link.download = `receipt-${mockPayment.receiptNumber}.pdf`
      // link.click()

      console.log("PDF downloaded successfully")
    } catch (error) {
      console.error("Error downloading PDF:", error)
    } finally {
      setIsDownloading(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 font-cairo"
      dir="rtl"
    >
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800 print:hidden">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                <ArrowRight className="w-5 h-5" />
                العودة للوحة التحكم
              </Link>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative w-10 h-10 rounded-xl overflow-hidden">
                <Image src="/diocese-logo.png" alt="شعار مطرانية شبين القناطر" fill className="object-cover" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-slate-900 dark:text-white">مشورة</h1>
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="mb-8 print:hidden">
            <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">إيصال الدفع</h1>
            <p className="text-slate-600 dark:text-slate-400">Payment Receipt</p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 mb-8 print:hidden">
            <Button
              onClick={handleDownloadPDF}
              disabled={isDownloading}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {isDownloading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin ml-2" />
                  جاري التحميل...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 ml-2" />
                  تحميل PDF
                </>
              )}
            </Button>

            <Button
              onClick={handlePrint}
              variant="outline"
              className="border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800 px-6 py-3 rounded-xl bg-transparent"
            >
              <Printer className="w-4 h-4 ml-2" />
              طباعة
            </Button>

            <Button
              variant="outline"
              className="border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800 px-6 py-3 rounded-xl bg-transparent"
            >
              <Share2 className="w-4 h-4 ml-2" />
              مشاركة
            </Button>
          </div>

          {/* Receipt Card */}
          <Card className="shadow-2xl border-0 bg-white dark:bg-slate-900">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-white">إيصال دفع</CardTitle>
                    <p className="text-blue-100">Payment Receipt</p>
                  </div>
                </div>
                <Badge className="bg-green-500 text-white px-4 py-2 text-sm">
                  <CheckCircle className="w-4 h-4 ml-1" />
                  {mockPayment.status}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="p-8 space-y-8">
              {/* Receipt Header Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pb-6 border-b border-slate-200 dark:border-slate-700">
                <div>
                  <h3 className="font-semibold text-slate-900 dark:text-white mb-4">معلومات الإيصال</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Hash className="w-4 h-4 text-blue-600" />
                      <span className="text-sm text-slate-600 dark:text-slate-400">رقم الإيصال:</span>
                      <span className="font-mono text-slate-900 dark:text-white">{mockPayment.receiptNumber}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-slate-600 dark:text-slate-400">التاريخ:</span>
                      <span className="text-slate-900 dark:text-white">
                        {mockPayment.date} - {mockPayment.time}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-slate-900 dark:text-white mb-4">بيانات الطالب</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm text-slate-600 dark:text-slate-400">الاسم:</span>
                      <span className="block font-semibold text-slate-900 dark:text-white">
                        {mockPayment.studentName}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-slate-600 dark:text-slate-400">البريد الإلكتروني:</span>
                      <span className="block text-slate-900 dark:text-white" dir="ltr">
                        {mockPayment.email}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-slate-600 dark:text-slate-400">رقم الهاتف:</span>
                      <span className="block text-slate-900 dark:text-white" dir="ltr">
                        {mockPayment.phone}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Course Information */}
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
                <h3 className="font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  تفاصيل الكورس
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-slate-600 dark:text-slate-400">اسم الكورس:</span>
                    <span className="block font-semibold text-slate-900 dark:text-white">{mockPayment.courseName}</span>
                  </div>
                  <div>
                    <span className="text-sm text-slate-600 dark:text-slate-400">نوع الاشتراك:</span>
                    <span className="block font-semibold text-slate-900 dark:text-white">اشتراك كامل</span>
                  </div>
                </div>
              </div>

              {/* Payment Details */}
              <div className="space-y-6">
                <h3 className="font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                  <CreditCard className="w-5 h-5 text-amber-600" />
                  تفاصيل الدفع
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between py-3 border-b border-slate-200 dark:border-slate-700">
                      <span className="text-slate-600 dark:text-slate-400">رقم المعاملة:</span>
                      <span className="font-mono text-slate-900 dark:text-white">{mockPayment.transactionId}</span>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-slate-200 dark:border-slate-700">
                      <span className="text-slate-600 dark:text-slate-400">طريقة الدفع:</span>
                      <span className="text-slate-900 dark:text-white">{mockPayment.paymentMethod}</span>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-slate-200 dark:border-slate-700">
                      <span className="text-slate-600 dark:text-slate-400">رقم البطاقة:</span>
                      <span className="font-mono text-slate-900 dark:text-white">
                        **** **** **** {mockPayment.cardLast4}
                      </span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-xl p-6">
                    <div className="text-center">
                      <DollarSign className="w-8 h-8 text-amber-600 mx-auto mb-3" />
                      <div className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                        {mockPayment.amount} {mockPayment.currency}
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">المبلغ المدفوع</div>
                      <div className="text-xs text-slate-500 dark:text-slate-500 mt-2">شامل جميع الرسوم</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="pt-6 border-t border-slate-200 dark:border-slate-700 text-center">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div className="relative w-12 h-12 rounded-xl overflow-hidden">
                    <Image src="/diocese-logo.png" alt="شعار مطرانية شبين القناطر" fill className="object-cover" />
                  </div>
                  <div>
                    <h4 className="font-bold text-slate-900 dark:text-white">مطرانية شبين القناطر</h4>
                    <p className="text-sm text-slate-600 dark:text-slate-400">Diocese of Shibin El Qanater</p>
                  </div>
                </div>
                <p className="text-xs text-slate-500 dark:text-slate-500">
                  هذا إيصال رسمي لدفع رسوم كورس الإعداد للزواج. يرجى الاحتفاظ به للمراجعة المستقبلية.
                </p>
                <p className="text-xs text-slate-400 dark:text-slate-600 mt-2">
                  This is an official receipt for the premarital course payment. Please keep it for future reference.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
