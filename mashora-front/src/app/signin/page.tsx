"use client";

import type React from "react";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ModernNavbar } from "@/components/modern-navbar";
import {
  Eye,
  EyeOff,
  Mail,
  Phone,
  Lock,
  ArrowLeft,
  Heart,
  Sparkles,
  AlertCircle,
  CheckCircle,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";

// Validation schema
const signinSchema = z.object({
  identifier: z.string().min(1, {
    message: "البريد الإلكتروني أو رقم الهاتف مطلوب",
  }),
  password: z.string().min(1, {
    message: "كلمة المرور مطلوبة",
  }),
});

type SigninFormData = z.infer<typeof signinSchema>;

export default function SigninPage() {
  const [formData, setFormData] = useState<SigninFormData>({
    identifier: "",
    password: "",
  });
  const [errors, setErrors] = useState<
    Partial<Record<keyof SigninFormData, string>>
  >({});
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");

  const handleInputChange = (field: keyof SigninFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    try {
      signinSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<Record<keyof SigninFormData, string>> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof SigninFormData] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setSubmitStatus("idle");

    try {
      // Simulate API call
      const response = await fetch("/auth/signin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setSubmitStatus("success");
        // Redirect to dashboard or course page
        setTimeout(() => {
          window.location.href = "/dashboard";
        }, 1500);
      } else {
        setSubmitStatus("error");
      }
    } catch (error) {
      setSubmitStatus("error");
    } finally {
      setIsLoading(false);
    }
  };

  const isEmailOrPhone = (identifier: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^(\+20|0020|20)?1[0125][0-9]{8}$/;
    return emailRegex.test(identifier) || phoneRegex.test(identifier);
  };

  return (
    <div
      className="min-h-screen bg-background text-foreground font-cairo overflow-x-hidden transition-colors duration-300"
      dir="rtl"
    >
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 dark:bg-gradient-to-br dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 light:bg-gradient-to-br light:from-amber-50 light:via-white light:to-orange-50 transition-all duration-500" />
        <div className="absolute inset-0 dark:bg-[radial-gradient(circle_at_50%_50%,rgba(251,191,36,0.1),transparent_50%)] light:bg-[radial-gradient(circle_at_50%_50%,rgba(30,41,59,0.1),transparent_50%)]" />
        <div className="absolute inset-0 dark:bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.1),transparent_50%)] light:bg-[radial-gradient(circle_at_80%_20%,rgba(251,191,36,0.1),transparent_50%)]" />
      </div>

      {/* Modern Navbar */}
      <ModernNavbar variant="minimal" />

      {/* Main Content */}
      <main className="relative z-10 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto">
            {/* Page Header */}
            <div className="text-center mb-8 animate-fade-in-up">
              <Badge className="mb-6 bg-gradient-to-r from-amber-500/20 to-orange-500/20 text-amber-600 dark:text-amber-300 hover:from-amber-500/30 hover:to-orange-500/30 text-sm px-6 py-3 border border-amber-500/30">
                <Heart className="w-4 h-4 ml-2" />
                تسجيل الدخول • Sign In
              </Badge>

              <h1 className="text-4xl font-black text-foreground mb-4">
                <span className="gradient-text">أهلاً بعودتك</span>
              </h1>
              <h2 className="text-xl font-semibold dark:text-slate-400 light:text-slate-600 mb-2">
                Welcome Back
              </h2>
              <p className="dark:text-slate-500 light:text-slate-500">
                سجل دخولك لمتابعة كورس الإعداد للزواج
              </p>
            </div>

            {/* Signin Form */}
            <Card className="glass-card border dark:border-slate-700/50 light:border-amber-200/50 shadow-2xl animate-scale-in">
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl font-bold text-foreground flex items-center justify-center gap-3">
                  <Lock className="w-6 h-6 text-amber-400" />
                  تسجيل الدخول
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Identifier Field */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="identifier"
                      className="text-foreground font-semibold flex items-center gap-2"
                    >
                      {formData.identifier &&
                      isEmailOrPhone(formData.identifier) ? (
                        formData.identifier.includes("@") ? (
                          <Mail className="w-4 h-4 text-amber-400" />
                        ) : (
                          <Phone className="w-4 h-4 text-amber-400" />
                        )
                      ) : (
                        <Mail className="w-4 h-4 text-amber-400" />
                      )}
                      البريد الإلكتروني أو رقم الهاتف *
                    </Label>
                    <Input
                      id="identifier"
                      type="text"
                      placeholder="<EMAIL> | 01012345678"
                      value={formData.identifier}
                      onChange={(e) =>
                        handleInputChange("identifier", e.target.value)
                      }
                      className={`text-right ${
                        errors.identifier
                          ? "border-red-500 focus:border-red-500"
                          : ""
                      }`}
                      dir="ltr"
                    />
                    {errors.identifier && (
                      <div className="flex items-center gap-2 text-red-500 text-sm">
                        <AlertCircle className="w-4 h-4" />
                        {errors.identifier}
                      </div>
                    )}
                  </div>

                  {/* Password Field */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="password"
                      className="text-foreground font-semibold flex items-center gap-2"
                    >
                      <Lock className="w-4 h-4 text-amber-400" />
                      كلمة المرور *
                    </Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="أدخل كلمة المرور"
                        value={formData.password}
                        onChange={(e) =>
                          handleInputChange("password", e.target.value)
                        }
                        className={`text-right ${
                          errors.password
                            ? "border-red-500 focus:border-red-500"
                            : ""
                        }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-amber-400 transition-colors"
                      >
                        {showPassword ? (
                          <EyeOff className="w-5 h-5" />
                        ) : (
                          <Eye className="w-5 h-5" />
                        )}
                      </button>
                    </div>
                    {errors.password && (
                      <div className="flex items-center gap-2 text-red-500 text-sm">
                        <AlertCircle className="w-4 h-4" />
                        {errors.password}
                      </div>
                    )}
                  </div>

                  {/* Forgot Password Link */}
                  <div className="text-left">
                    <Link
                      href="/forgot-password"
                      className="text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 text-sm font-semibold transition-colors"
                    >
                      نسيت كلمة المرور؟
                    </Link>
                  </div>

                  {/* Submit Status */}
                  {submitStatus === "success" && (
                    <div className="flex items-center gap-2 text-green-600 text-sm bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                      <CheckCircle className="w-4 h-4" />
                      تم تسجيل الدخول بنجاح! جاري التوجيه...
                    </div>
                  )}

                  {submitStatus === "error" && (
                    <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                      <AlertCircle className="w-4 h-4" />
                      خطأ في البيانات. تأكد من صحة البريد الإلكتروني وكلمة
                      المرور
                    </div>
                  )}

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white text-lg py-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 ml-2 animate-spin" />
                        جاري تسجيل الدخول...
                      </>
                    ) : (
                      <>
                        تسجيل الدخول
                        <ArrowLeft className="w-5 h-5 mr-2" />
                      </>
                    )}
                  </Button>
                </form>

                {/* Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t dark:border-slate-700 light:border-amber-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 dark:bg-slate-900 light:bg-white dark:text-slate-400 light:text-slate-600">
                      أو
                    </span>
                  </div>
                </div>

                {/* Signup Link */}
                <div className="text-center">
                  <p className="dark:text-slate-400 light:text-slate-600 mb-4">
                    ليس لديك حساب؟
                  </p>
                  <Link href="/signup">
                    <Button
                      variant="outline"
                      className="w-full border-2 dark:border-slate-600 light:border-amber-300 dark:text-slate-300 light:text-amber-700 dark:hover:bg-slate-800 light:hover:bg-amber-50 dark:hover:border-slate-500 light:hover:border-amber-400 text-lg py-6 rounded-xl bg-transparent backdrop-blur-sm transition-all duration-300 transform hover:scale-105"
                    >
                      <Sparkles className="w-5 h-5 ml-2" />
                      إنشاء حساب جديد
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Back to Home */}
            <div className="text-center mt-8 animate-fade-in-up">
              <Link
                href="/"
                className="inline-flex items-center gap-2 dark:text-slate-400 light:text-slate-600 hover:text-amber-400 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                العودة للصفحة الرئيسية
              </Link>
            </div>
          </div>
        </div>
      </main>

      {/* Floating Elements */}
      <div className="absolute top-20 right-10 w-20 h-20 floating-element rounded-full animate-float" />
      <div
        className="absolute bottom-20 left-10 w-16 h-16 floating-element rounded-full animate-float"
        style={{ animationDelay: "2s" }}
      />
    </div>
  );
}
