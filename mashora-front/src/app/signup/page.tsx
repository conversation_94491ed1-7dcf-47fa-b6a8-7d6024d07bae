"use client";

import type React from "react";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ModernNavbar } from "@/components/modern-navbar";
import {
  Eye,
  EyeOff,
  User,
  Mail,
  Phone,
  Lock,
  Calendar,
  MapPin,
  Church,
  Upload,
  X,
  ArrowLeft,
  Heart,
  Sparkles,
  AlertCircle,
  CheckCircle,
  Loader2,
  CreditCard,
  UserCheck,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { z } from "zod";

// Validation schema
const signupSchema = z
  .object({
    firstName: z.string().min(2, { message: "الاسم الأول مطلوب" }).max(50),
    lastName: z.string().min(2, { message: "الاسم الأخير مطلوب" }).max(50),
    email: z.string().email({ message: "البريد الإلكتروني غير صالح" }),
    phone: z.string().regex(/^(\+20|0020|20)?1[0125][0-9]{8}$/, {
      message: "رقم الهاتف المصري غير صالح",
    }),
    password: z
      .string()
      .min(8, { message: "كلمة المرور يجب أن تكون على الأقل 8 أحرف" }),
    confirmPassword: z.string(),
    nationalId: z.string().regex(/^[0-9]{14}$/, {
      message: "الرقم القومي يجب أن يتكون من 14 رقمًا",
    }),
    dateOfBirth: z.string().min(1, { message: "تاريخ الميلاد مطلوب" }),
    address: z.string().min(10, { message: "العنوان مطلوب" }).max(200),
    churchBelong: z.string().min(2, { message: "الكنيسة التابع لها مطلوبة" }),
    fatherOfConfession: z.string().min(2, { message: "أب الاعتراف مطلوب" }),
    confessionChurch: z
      .string()
      .min(2, { message: "كنيسة أب الاعتراف مطلوبة" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "كلمة المرور غير متطابقة",
    path: ["confirmPassword"],
  });

type SignupFormData = z.infer<typeof signupSchema>;

interface FileUploads {
  avatar?: File;
  nationalIdFront?: File;
  nationalIdBack?: File;
}

export default function SignupPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<
    SignupFormData & { paymentMethod?: string }
  >({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    nationalId: "",
    dateOfBirth: "",
    address: "",
    churchBelong: "",
    fatherOfConfession: "",
    confessionChurch: "",
    paymentMethod: "card",
  });
  const [fileUploads, setFileUploads] = useState<FileUploads>({});
  const [errors, setErrors] = useState<
    Partial<Record<keyof SignupFormData, string>>
  >({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");

  const totalSteps = 5;

  const handleInputChange = (
    field: keyof (SignupFormData & { paymentMethod?: string }),
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFileChange = (field: keyof FileUploads, file: File | null) => {
    setFileUploads((prev) => ({ ...prev, [field]: file || undefined }));
  };

  const validateCurrentStep = (): boolean => {
    const stepFields: Record<number, (keyof SignupFormData)[]> = {
      1: ["firstName", "lastName", "email", "phone"],
      2: ["password", "confirmPassword"],
      3: ["nationalId", "dateOfBirth", "address"],
      4: ["churchBelong", "fatherOfConfession", "confessionChurch"],
      5: [], // Payment step - no required fields for now
    };

    const fieldsToValidate = stepFields[currentStep] || [];

    // Clear previous errors
    setErrors({});

    // Validate each field individually for better error handling
    const newErrors: Partial<Record<keyof SignupFormData, string>> = {};
    let isValid = true;

    fieldsToValidate.forEach((field) => {
      const value = formData[field];

      // Check if field is empty
      if (!value || value.trim() === "") {
        switch (field) {
          case "firstName":
            newErrors.firstName = "الاسم الأول مطلوب";
            break;
          case "lastName":
            newErrors.lastName = "الاسم الأخير مطلوب";
            break;
          case "email":
            newErrors.email = "البريد الإلكتروني مطلوب";
            break;
          case "phone":
            newErrors.phone = "رقم الهاتف مطلوب";
            break;
          case "password":
            newErrors.password = "كلمة المرور مطلوبة";
            break;
          case "confirmPassword":
            newErrors.confirmPassword = "تأكيد كلمة المرور مطلوب";
            break;
          case "nationalId":
            newErrors.nationalId = "الرقم القومي مطلوب";
            break;
          case "dateOfBirth":
            newErrors.dateOfBirth = "تاريخ الميلاد مطلوب";
            break;
          case "address":
            newErrors.address = "العنوان مطلوب";
            break;
          case "churchBelong":
            newErrors.churchBelong = "الكنيسة التابع لها مطلوبة";
            break;
          case "fatherOfConfession":
            newErrors.fatherOfConfession = "أب الاعتراف مطلوب";
            break;
          case "confessionChurch":
            newErrors.confessionChurch = "كنيسة أب الاعتراف مطلوبة";
            break;
        }
        isValid = false;
        return;
      }

      // Validate specific field formats
      switch (field) {
        case "firstName":
        case "lastName":
          if (value.length < 2) {
            newErrors[field] =
              field === "firstName"
                ? "الاسم الأول قصير جداً"
                : "الاسم الأخير قصير جداً";
            isValid = false;
          }
          break;

        case "email":
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            newErrors.email = "البريد الإلكتروني غير صالح";
            isValid = false;
          }
          break;

        case "phone":
          const phoneRegex = /^(\+20|0020|20)?1[0125][0-9]{8}$/;
          if (!phoneRegex.test(value)) {
            newErrors.phone = "رقم الهاتف المصري غير صالح";
            isValid = false;
          }
          break;

        case "password":
          if (value.length < 8) {
            newErrors.password = "كلمة المرور يجب أن تكون على الأقل 8 أحرف";
            isValid = false;
          }
          break;

        case "confirmPassword":
          if (value !== formData.password) {
            newErrors.confirmPassword = "كلمة المرور غير متطابقة";
            isValid = false;
          }
          break;

        case "nationalId":
          if (!/^[0-9]{14}$/.test(value)) {
            newErrors.nationalId = "الرقم القومي يجب أن يتكون من 14 رقمًا";
            isValid = false;
          }
          break;

        case "address":
          if (value.length < 10) {
            newErrors.address = "العنوان قصير جداً";
            isValid = false;
          }
          break;
      }
    });

    if (!isValid) {
      setErrors(newErrors);
    }

    console.log(
      "Validation result:",
      isValid,
      "Errors:",
      newErrors,
      "Form data:",
      formData
    );
    return isValid;
  };

  const nextStep = () => {
    console.log("nextStep called, current step:", currentStep);
    console.log("Current form data:", formData);

    const isValid = validateCurrentStep();
    console.log("Validation result:", isValid);

    if (isValid && currentStep < totalSteps) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      console.log("Moving to step:", newStep);
    } else {
      console.log("Validation failed or already at last step");
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateCurrentStep()) return;

    setIsLoading(true);
    setSubmitStatus("idle");

    try {
      const formDataToSend = new FormData();

      // Add text fields
      Object.entries(formData).forEach(([key, value]) => {
        formDataToSend.append(key, value);
      });

      // Add files
      Object.entries(fileUploads).forEach(([key, file]) => {
        if (file) {
          formDataToSend.append(key, file);
        }
      });

      const response = await fetch("/auth/signup", {
        method: "POST",
        body: formDataToSend,
      });

      if (response.ok) {
        setSubmitStatus("success");
        setTimeout(() => {
          window.location.href = "/signin";
        }, 2000);
      } else {
        setSubmitStatus("error");
      }
    } catch (error) {
      setSubmitStatus("error");
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-foreground mb-2">
                البيانات الشخصية
              </h3>
              <p className="dark:text-slate-400 light:text-slate-600">
                Personal Information
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="firstName"
                  className="text-foreground font-semibold flex items-center gap-2"
                >
                  <User className="w-4 h-4 text-amber-400" />
                  الاسم الأول *
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  placeholder="أبانوب"
                  value={formData.firstName}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  className={`text-right ${
                    errors.firstName ? "border-red-500" : ""
                  }`}
                />
                {errors.firstName && (
                  <div className="flex items-center gap-2 text-red-500 text-sm">
                    <AlertCircle className="w-4 h-4" />
                    {errors.firstName}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="lastName"
                  className="text-foreground font-semibold flex items-center gap-2"
                >
                  <User className="w-4 h-4 text-amber-400" />
                  الاسم الأخير *
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  placeholder="نشأت"
                  value={formData.lastName}
                  onChange={(e) =>
                    handleInputChange("lastName", e.target.value)
                  }
                  className={`text-right ${
                    errors.lastName ? "border-red-500" : ""
                  }`}
                />
                {errors.lastName && (
                  <div className="flex items-center gap-2 text-red-500 text-sm">
                    <AlertCircle className="w-4 h-4" />
                    {errors.lastName}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="email"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <Mail className="w-4 h-4 text-amber-400" />
                البريد الإلكتروني *
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`text-right ${errors.email ? "border-red-500" : ""}`}
                dir="ltr"
              />
              {errors.email && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.email}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="phone"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <Phone className="w-4 h-4 text-amber-400" />
                رقم الهاتف *
              </Label>
              <Input
                id="phone"
                type="tel"
                placeholder="01012345678"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className={`text-right ${errors.phone ? "border-red-500" : ""}`}
                dir="ltr"
              />
              {errors.phone && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.phone}
                </div>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-foreground mb-2">
                كلمة المرور
              </h3>
              <p className="dark:text-slate-400 light:text-slate-600">
                Password Setup
              </p>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="password"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <Lock className="w-4 h-4 text-amber-400" />
                كلمة المرور *
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="أدخل كلمة مرور قوية"
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange("password", e.target.value)
                  }
                  className={`text-right pr-12 ${
                    errors.password ? "border-red-500" : ""
                  }`}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-amber-400 transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.password}
                </div>
              )}
              <p className="text-sm dark:text-slate-400 light:text-slate-600">
                يجب أن تحتوي على 8 أحرف على الأقل
              </p>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="confirmPassword"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <Lock className="w-4 h-4 text-amber-400" />
                تأكيد كلمة المرور *
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="أعد إدخال كلمة المرور"
                  value={formData.confirmPassword}
                  onChange={(e) =>
                    handleInputChange("confirmPassword", e.target.value)
                  }
                  className={`text-right pr-12 ${
                    errors.confirmPassword ? "border-red-500" : ""
                  }`}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-amber-400 transition-colors"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.confirmPassword}
                </div>
              )}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-foreground mb-2">
                بيانات الهوية
              </h3>
              <p className="dark:text-slate-400 light:text-slate-600">
                Identity Information
              </p>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="nationalId"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <CreditCard className="w-4 h-4 text-amber-400" />
                الرقم القومي *
              </Label>
              <Input
                id="nationalId"
                type="text"
                placeholder="12345678901234"
                maxLength={14}
                value={formData.nationalId}
                onChange={(e) =>
                  handleInputChange(
                    "nationalId",
                    e.target.value.replace(/\D/g, "")
                  )
                }
                className={`text-right ${
                  errors.nationalId ? "border-red-500" : ""
                }`}
                dir="ltr"
              />
              {errors.nationalId && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.nationalId}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="dateOfBirth"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <Calendar className="w-4 h-4 text-amber-400" />
                تاريخ الميلاد *
              </Label>
              <Input
                id="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) =>
                  handleInputChange("dateOfBirth", e.target.value)
                }
                className={`text-right ${
                  errors.dateOfBirth ? "border-red-500" : ""
                }`}
              />
              {errors.dateOfBirth && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.dateOfBirth}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="address"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <MapPin className="w-4 h-4 text-amber-400" />
                العنوان *
              </Label>
              <Textarea
                id="address"
                placeholder="العنوان بالتفصيل"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                className={`text-right min-h-[100px] ${
                  errors.address ? "border-red-500" : ""
                }`}
              />
              {errors.address && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.address}
                </div>
              )}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-foreground mb-2">
                البيانات الكنسية
              </h3>
              <p className="dark:text-slate-400 light:text-slate-600">
                Church Information
              </p>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="churchBelong"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <Church className="w-4 h-4 text-amber-400" />
                الكنيسة التابع لها *
              </Label>
              <select
                id="churchBelong"
                value={formData.churchBelong}
                onChange={(e) =>
                  handleInputChange("churchBelong", e.target.value)
                }
                className={`w-full px-3 py-2 text-right bg-background border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 ${
                  errors.churchBelong ? "border-red-500" : "border-input"
                }`}
              >
                <option value="">اختر الكنيسة</option>
                <option value="السيدة العذراء والقديس يوسف النجار - الخصوص">
                  السيدة العذراء والقديس يوسف النجار - الخصوص
                </option>
                <option value="السيدة العذراء والرسولي بطرس وبولس - الخصوص">
                  السيدة العذراء والرسولي بطرس وبولس - الخصوص
                </option>
                <option value="البابا أثناسيوس الرسول والانبا بيشوي - الخصوص">
                  البابا أثناسيوس الرسول والانبا بيشوي - الخصوص
                </option>
                <option value="السيدة العذراء والقديس ابى سيفين - الخصوص">
                  السيدة العذراء والقديس ابى سيفين - الخصوص
                </option>
                <option value="الانبا كاراس والانبا ابرام - الخصوص">
                  الانبا كاراس والانبا ابرام - الخصوص
                </option>
                <option value="السيدة العذراء والشهيد العظيم أبانوب - الخصوص">
                  السيدة العذراء والشهيد العظيم أبانوب - الخصوص
                </option>
                <option value="السيدة العذراء والانبا موسى - الخصوص">
                  السيدة العذراء والانبا موسى - الخصوص
                </option>
                <option value="السيدة العذراء والملاك ميخائيل - الخصوص">
                  السيدة العذراء والملاك ميخائيل - الخصوص
                </option>
                <option value="الشهيد العظيم مارمينا والبابا كيرلس السادس - الخصوص">
                  الشهيد العظيم مارمينا والبابا كيرلس السادس - الخصوص
                </option>
                <option value="الشهيد العظيم مارجرجس والبابا ديسقوروس - الخصوص">
                  الشهيد العظيم مارجرجس والبابا ديسقوروس - الخصوص
                </option>
                <option value="السيدة العذراء والقديس ماريوحنا الحبيب - الخصوص">
                  السيدة العذراء والقديس ماريوحنا الحبيب - الخصوص
                </option>
                <option value="الشهيد العظيم مارجرجس - قها">
                  الشهيد العظيم مارجرجس - قها
                </option>
                <option value="الشهيد العظيم مارجرجس - طوخ">
                  الشهيد العظيم مارجرجس - طوخ
                </option>
                <option value="القديسة الشهيدة دميانة - ميت كنانه">
                  القديسة الشهيدة دميانة - ميت كنانه
                </option>
                <option value="الشهيد العظيم مارجرجس - بلتان">
                  الشهيد العظيم مارجرجس - بلتان
                </option>
                <option value="الشهيد العظيم مارمينا العجايبي - ساحل دجوي">
                  الشهيد العظيم مارمينا العجايبي - ساحل دجوي
                </option>
                <option value="السيدة العذراء والقديس العظيم ابى سيفين - ساحل دجوى">
                  السيدة العذراء والقديس العظيم ابى سيفين - ساحل دجوى
                </option>
                <option value="رئيس الملائكة الجليل ميخائيل - القلزم">
                  رئيس الملائكة الجليل ميخائيل - القلزم
                </option>
                <option value="السيدة العذراء والقديس مارمرقس الرسول - كفر شبين">
                  السيدة العذراء والقديس مارمرقس الرسول - كفر شبين
                </option>
                <option value="الشهيد العظيم مارجرجس - منيه شبين">
                  الشهيد العظيم مارجرجس - منيه شبين
                </option>
                <option value="البابا كيرلس السادس - الحصافة">
                  البابا كيرلس السادس - الحصافة
                </option>
                <option value="رئيس الملائكة الجليل ميخائيل - القشيش">
                  رئيس الملائكة الجليل ميخائيل - القشيش
                </option>
                <option value="السيدة العذراء والقديس ابى سيفين - السلمانية">
                  السيدة العذراء والقديس ابى سيفين - السلمانية
                </option>
                <option value="الشهيد العظيم مارجرجس والانبا كاراس - نوى">
                  الشهيد العظيم مارجرجس والانبا كاراس - نوى
                </option>
                <option value="السيدة العذراء - مساكن ابو زعبل">
                  السيدة العذراء - مساكن ابو زعبل
                </option>
                <option value="الشهيد العظيم مارجرجس - ابو زعبل">
                  الشهيد العظيم مارجرجس - ابو زعبل
                </option>
                <option value="رئيس الملائكة الجليل ميخائيل - العكرشة">
                  رئيس الملائكة الجليل ميخائيل - العكرشة
                </option>
                <option value="البابا بطرس خاتم الشهداء - الخانكة">
                  البابا بطرس خاتم الشهداء - الخانكة
                </option>
                <option value="الشهيد العظيم مارمينا والبابا كيرلس السادس - الجبل الاصفر">
                  الشهيد العظيم مارمينا والبابا كيرلس السادس - الجبل الاصفر
                </option>
                <option value="السيدة العذراء والقديس ابانوب - الفلج">
                  السيدة العذراء والقديس ابانوب - الفلج
                </option>
                <option value="الشهيد العظيم ابى سيفين والقديسة دميانة - الفلج">
                  الشهيد العظيم ابى سيفين والقديسة دميانة - الفلج
                </option>
                <option value="السيدة العذراء والامير تادرس - الفلج">
                  السيدة العذراء والامير تادرس - الفلج
                </option>
                <option value="السيدة العذراء والانبا بيشوي - المنيا">
                  السيدة العذراء والانبا بيشوي - المنيا
                </option>
                <option value="مديح الامين - ارض عبقة - الخصوص">
                  مديح الامين - ارض عبقة - الخصوص
                </option>
              </select>
              {errors.churchBelong && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.churchBelong}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="fatherOfConfession"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <UserCheck className="w-4 h-4 text-amber-400" />
                أب الاعتراف *
              </Label>
              <Input
                id="fatherOfConfession"
                type="text"
                placeholder="أبونا يوسف"
                value={formData.fatherOfConfession}
                onChange={(e) =>
                  handleInputChange("fatherOfConfession", e.target.value)
                }
                className={`text-right ${
                  errors.fatherOfConfession ? "border-red-500" : ""
                }`}
              />
              {errors.fatherOfConfession && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.fatherOfConfession}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="confessionChurch"
                className="text-foreground font-semibold flex items-center gap-2"
              >
                <Church className="w-4 h-4 text-amber-400" />
                كنيسة أب الاعتراف *
              </Label>
              <Input
                id="confessionChurch"
                type="text"
                placeholder="كنيسة مارجرجس"
                value={formData.confessionChurch}
                onChange={(e) =>
                  handleInputChange("confessionChurch", e.target.value)
                }
                className={`text-right ${
                  errors.confessionChurch ? "border-red-500" : ""
                }`}
              />
              {errors.confessionChurch && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {errors.confessionChurch}
                </div>
              )}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-foreground mb-2">
                الدفع والملفات
              </h3>
              <p className="dark:text-slate-400 light:text-slate-600">
                Payment & Files
              </p>
            </div>

            {/* Payment Section */}
            <div className="space-y-4 p-6 glass-card rounded-xl border dark:border-slate-700/50 light:border-amber-200/50">
              <h4 className="font-semibold text-foreground flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-amber-400" />
                طريقة الدفع - 200 جنيه
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                    formData.paymentMethod === "card"
                      ? "border-amber-500 bg-amber-50 dark:bg-amber-900/20"
                      : "border-slate-300 dark:border-slate-600 hover:border-amber-400"
                  }`}
                  onClick={() => handleInputChange("paymentMethod", "card")}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.paymentMethod === "card"
                          ? "border-amber-500 bg-amber-500"
                          : "border-slate-400"
                      }`}
                    >
                      {formData.paymentMethod === "card" && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                      )}
                    </div>
                    <div>
                      <h5 className="font-semibold text-foreground">
                        بطاقة ائتمان
                      </h5>
                      <p className="text-sm dark:text-slate-400 light:text-slate-600">
                        Credit Card
                      </p>
                    </div>
                  </div>
                </div>

                <div
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                    formData.paymentMethod === "cash"
                      ? "border-amber-500 bg-amber-50 dark:bg-amber-900/20"
                      : "border-slate-300 dark:border-slate-600 hover:border-amber-400"
                  }`}
                  onClick={() => handleInputChange("paymentMethod", "cash")}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.paymentMethod === "cash"
                          ? "border-amber-500 bg-amber-500"
                          : "border-slate-400"
                      }`}
                    >
                      {formData.paymentMethod === "cash" && (
                        <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                      )}
                    </div>
                    <div>
                      <h5 className="font-semibold text-foreground">
                        دفع نقدي
                      </h5>
                      <p className="text-sm dark:text-slate-400 light:text-slate-600">
                        Cash Payment
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-800 dark:text-blue-300">
                  💡 يمكنك الدفع نقدياً في مكتب المطرانية أو عبر البطاقة
                  الائتمانية الآن
                </p>
              </div>
            </div>

            {/* File Uploads Section */}
            <div className="space-y-4 p-6 glass-card rounded-xl border dark:border-slate-700/50 light:border-amber-200/50">
              <h4 className="font-semibold text-foreground flex items-center gap-2">
                <Upload className="w-5 h-5 text-amber-400" />
                الملفات (اختيارية)
              </h4>

              <div className="grid grid-cols-1 gap-4">
                <FileUploadField
                  label="الصورة الشخصية"
                  file={fileUploads.avatar}
                  onChange={(file) => handleFileChange("avatar", file)}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FileUploadField
                    label="صورة البطاقة (الوجه)"
                    file={fileUploads.nationalIdFront}
                    onChange={(file) =>
                      handleFileChange("nationalIdFront", file)
                    }
                  />

                  <FileUploadField
                    label="صورة البطاقة (الظهر)"
                    file={fileUploads.nationalIdBack}
                    onChange={(file) =>
                      handleFileChange("nationalIdBack", file)
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className="min-h-screen bg-background text-foreground font-cairo overflow-x-hidden transition-colors duration-300"
      dir="rtl"
    >
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 dark:bg-gradient-to-br dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 light:bg-gradient-to-br light:from-amber-50 light:via-white light:to-orange-50 transition-all duration-500" />
        <div className="absolute inset-0 dark:bg-[radial-gradient(circle_at_50%_50%,rgba(251,191,36,0.1),transparent_50%)] light:bg-[radial-gradient(circle_at_50%_50%,rgba(30,41,59,0.1),transparent_50%)]" />
        <div className="absolute inset-0 dark:bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.1),transparent_50%)] light:bg-[radial-gradient(circle_at_80%_20%,rgba(251,191,36,0.1),transparent_50%)]" />
      </div>

      {/* Modern Navbar */}
      <ModernNavbar variant="minimal" />

      {/* Main Content */}
      <main className="relative z-10 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            {/* Page Header */}
            <div className="text-center mb-8 animate-fade-in-up">
              <Badge className="mb-6 bg-gradient-to-r from-amber-500/20 to-orange-500/20 text-amber-600 dark:text-amber-300 hover:from-amber-500/30 hover:to-orange-500/30 text-sm px-6 py-3 border border-amber-500/30">
                <Heart className="w-4 h-4 ml-2" />
                إنشاء حساب جديد • Sign Up
              </Badge>

              <h1 className="text-4xl font-black text-foreground mb-4">
                <span className="gradient-text">انضم إلينا</span>
              </h1>
              <h2 className="text-xl font-semibold dark:text-slate-400 light:text-slate-600 mb-2">
                Join Us
              </h2>
              <p className="dark:text-slate-500 light:text-slate-500">
                أنشئ حسابك لبدء كورس الإعداد للزواج
              </p>
            </div>

            {/* Progress Steps */}
            <div className="mb-8 animate-scale-in">
              <div className="flex items-center justify-center mb-4">
                {Array.from({ length: totalSteps }, (_, i) => (
                  <div key={i} className="flex items-center">
                    <div
                      className={`relative w-12 h-12 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-500 ${
                        i + 1 < currentStep
                          ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg"
                          : i + 1 === currentStep
                          ? "bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg animate-pulse"
                          : "dark:bg-slate-700 light:bg-amber-100 dark:text-slate-400 light:text-slate-600"
                      }`}
                    >
                      {i + 1 < currentStep ? (
                        <CheckCircle className="w-6 h-6" />
                      ) : (
                        i + 1
                      )}
                      {i + 1 === currentStep && (
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 animate-ping opacity-20"></div>
                      )}
                    </div>
                    {i < totalSteps - 1 && (
                      <div
                        className={`w-20 h-2 mx-3 rounded-full transition-all duration-500 ${
                          i + 1 < currentStep
                            ? "bg-gradient-to-r from-green-500 to-emerald-500"
                            : i + 1 === currentStep
                            ? "bg-gradient-to-r from-amber-500/50 to-orange-500/50"
                            : "dark:bg-slate-700 light:bg-amber-200"
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>
              <div className="text-center">
                <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass-card">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 animate-pulse"></div>
                  <span className="text-sm font-semibold text-foreground">
                    الخطوة {currentStep} من {totalSteps}
                  </span>
                  <span className="text-xs dark:text-slate-400 light:text-slate-600">
                    {currentStep === 1 && "البيانات الشخصية"}
                    {currentStep === 2 && "كلمة المرور"}
                    {currentStep === 3 && "بيانات الهوية"}
                    {currentStep === 4 && "البيانات الكنسية"}
                    {currentStep === 5 && "الدفع والملفات"}
                  </span>
                </div>
              </div>
            </div>

            {/* Signup Form */}
            <Card className="glass-card border dark:border-slate-700/50 light:border-amber-200/50 shadow-2xl animate-scale-in">
              <CardContent className="p-8">
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    if (currentStep === totalSteps) {
                      handleSubmit(e);
                    } else {
                      nextStep();
                    }
                  }}
                >
                  {renderStepContent()}

                  {/* Submit Status */}
                  {submitStatus === "success" && (
                    <div className="flex items-center gap-2 text-green-600 text-sm bg-green-50 dark:bg-green-900/20 p-3 rounded-lg mt-6">
                      <CheckCircle className="w-4 h-4" />
                      تم إنشاء الحساب بنجاح! جاري التوجيه لصفحة تسجيل الدخول...
                    </div>
                  )}

                  {submitStatus === "error" && (
                    <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg mt-6">
                      <AlertCircle className="w-4 h-4" />
                      حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى
                    </div>
                  )}

                  {/* Navigation Buttons */}
                  <div className="flex justify-between items-center mt-8 pt-6 border-t dark:border-slate-700 light:border-amber-200">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={prevStep}
                      disabled={currentStep === 1}
                      className="border-2 dark:border-slate-600 light:border-amber-300 dark:text-slate-300 light:text-amber-700 dark:hover:bg-slate-800 light:hover:bg-amber-50 disabled:opacity-50 bg-transparent px-6 py-3 rounded-xl transition-all duration-300"
                    >
                      <ArrowLeft className="w-4 h-4 ml-2" />
                      السابق
                    </Button>

                    <div className="text-center">
                      <span className="text-xs dark:text-slate-500 light:text-slate-500">
                        {Math.round((currentStep / totalSteps) * 100)}% مكتمل
                      </span>
                    </div>

                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                          جاري الإنشاء...
                        </>
                      ) : currentStep === totalSteps ? (
                        <>
                          إنشاء الحساب
                          <Sparkles className="w-4 h-4 mr-2" />
                        </>
                      ) : (
                        <>
                          التالي
                          <ArrowLeft className="w-4 h-4 mr-2 rotate-180" />
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Signin Link */}
            <div className="text-center mt-8 animate-fade-in-up">
              <p className="dark:text-slate-400 light:text-slate-600 mb-4">
                لديك حساب بالفعل؟
              </p>
              <Link
                href="/signin"
                className="inline-flex items-center gap-2 text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 font-semibold transition-colors"
              >
                تسجيل الدخول
                <ArrowLeft className="w-4 h-4" />
              </Link>
            </div>

            {/* Back to Home */}
            <div className="text-center mt-4 animate-fade-in-up">
              <Link
                href="/"
                className="inline-flex items-center gap-2 dark:text-slate-400 light:text-slate-600 hover:text-amber-400 transition-colors text-sm"
              >
                <ArrowLeft className="w-4 h-4" />
                العودة للصفحة الرئيسية
              </Link>
            </div>
          </div>
        </div>
      </main>

      {/* Floating Elements */}
      <div className="absolute top-20 right-10 w-20 h-20 floating-element rounded-full animate-float" />
      <div
        className="absolute bottom-20 left-10 w-16 h-16 floating-element rounded-full animate-float"
        style={{ animationDelay: "2s" }}
      />
    </div>
  );
}

// File Upload Component
function FileUploadField({
  label,
  file,
  onChange,
}: {
  label: string;
  file?: File;
  onChange: (file: File | null) => void;
}) {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    onChange(selectedFile);
  };

  const removeFile = () => {
    onChange(null);
  };

  return (
    <div className="space-y-2">
      <Label className="text-foreground font-semibold flex items-center gap-2">
        <Upload className="w-4 h-4 text-amber-400" />
        {label}
      </Label>

      {!file ? (
        <div className="relative border-2 border-dashed dark:border-slate-600 light:border-amber-300 rounded-lg p-6 text-center hover:border-amber-400 transition-colors">
          <Upload className="w-8 h-8 text-amber-400 mx-auto mb-2" />
          <p className="dark:text-slate-400 light:text-slate-600 mb-2">
            اضغط لاختيار الملف
          </p>
          <input
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
            onChange={handleFileChange}
            className="absolute w-full h-full opacity-0 top-0 left-0 cursor-pointer"
          />
          <p className="text-xs dark:text-slate-500 light:text-slate-500">
            JPEG, PNG, WebP (حد أقصى 5MB)
          </p>
        </div>
      ) : (
        <div className="flex items-center justify-between p-3 dark:bg-slate-800 light:bg-amber-50 rounded-lg">
          <span className="text-sm text-foreground truncate">{file.name}</span>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={removeFile}
            className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
