"use client"

import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Home, ArrowRight, Menu, X, User, LogOut } from "lucide-react"
import { cn } from "@/lib/utils"
import { useScroll } from "@/hooks/use-scroll"
import { But<PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

interface NavItem {
  href: string
  label: string
  icon?: React.ReactNode
  isActive?: boolean
}

interface ModernNavbarProps {
  variant?: "default" | "minimal" | "dashboard"
  showBackButton?: boolean
  backHref?: string
  backLabel?: string
  navItems?: NavItem[]
  showUserMenu?: boolean
  userInfo?: {
    name: string
    email?: string
    avatar?: string
  }
  className?: string
}

export function ModernNavbar({
  variant = "default",
  showBackButton = false,
  backHref = "/",
  backLabel = "العودة",
  navItems = [],
  showUserMenu = false,
  userInfo,
  className,
}: ModernNavbarProps) {
  const pathname = usePathname()
  const { scrollY, isScrolled, isScrolledPast } = useScroll()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)

  // Calculate dynamic styles based on scroll
  const navbarOpacity = isScrolled ? 0.95 : 1
  const navbarScale = isScrolledPast(100) ? 0.98 : 1
  const shadowIntensity = Math.min(scrollY / 100, 1)

  const defaultNavItems: NavItem[] = [
    {
      href: "/dashboard",
      label: "لوحة التحكم",
      icon: <Home className="w-4 h-4" />,
      isActive: pathname === "/dashboard",
    },
  ]

  const currentNavItems = navItems.length > 0 ? navItems : defaultNavItems

  return (
    <header
      className={cn(
        // Base styles
        "sticky top-0 z-50 w-full transition-all duration-300 ease-out",
        // Modern design with rounded corners and backdrop blur
        "bg-white/80 dark:bg-slate-900/80 backdrop-blur-lg",
        "border-b border-slate-200/50 dark:border-slate-800/50",
        // Responsive padding
        "px-4 py-3 md:px-6 md:py-4",
        className
      )}
      style={{
        opacity: navbarOpacity,
        transform: `scale(${navbarScale})`,
        boxShadow: `0 4px 20px rgba(0, 0, 0, ${shadowIntensity * 0.1})`,
      }}
    >
      <div className="container mx-auto">
        <div className="flex items-center justify-between">
          {/* Left Section - Logo/Back Button */}
          <div className="flex items-center gap-4">
            {showBackButton ? (
              <Link
                href={backHref}
                className={cn(
                  "flex items-center gap-2 text-slate-600 dark:text-slate-400",
                  "hover:text-blue-600 dark:hover:text-blue-400",
                  "transition-all duration-200 ease-out",
                  "hover:scale-105 active:scale-95"
                )}
              >
                <ArrowRight className="w-5 h-5" />
                <span className="hidden sm:inline">{backLabel}</span>
              </Link>
            ) : (
              <Link
                href="/"
                className={cn(
                  "flex items-center gap-4 group",
                  "transition-all duration-300 ease-out",
                  "hover:scale-105 active:scale-95"
                )}
              >
                <div className="relative w-12 h-12 md:w-14 md:h-14 rounded-xl overflow-hidden">
                  <Image
                    src="/diocese-logo.png"
                    alt="شعار مطرانية شبين القناطر"
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    priority
                  />
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-lg md:text-xl font-bold text-slate-900 dark:text-white">
                    مشورة
                  </h1>
                  <p className="text-xs md:text-sm text-slate-600 dark:text-slate-400">
                    مطرانية شبين القناطر
                  </p>
                </div>
              </Link>
            )}
          </div>

          {/* Center Section - Navigation (Desktop) */}
          {variant === "dashboard" && (
            <nav className="hidden md:flex items-center gap-6">
              {currentNavItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 rounded-lg",
                    "transition-all duration-200 ease-out",
                    "hover:bg-slate-100 dark:hover:bg-slate-800",
                    "hover:scale-105 active:scale-95",
                    item.isActive
                      ? "text-blue-600 dark:text-blue-400 font-semibold bg-blue-50 dark:bg-blue-900/20"
                      : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100"
                  )}
                >
                  {item.icon}
                  {item.label}
                </Link>
              ))}
            </nav>
          )}

          {/* Right Section - Actions */}
          <div className="flex items-center gap-3">
            <ThemeToggle />

            {/* User Menu (Desktop) */}
            {showUserMenu && userInfo && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "hidden md:flex items-center gap-2",
                      "hover:bg-slate-100 dark:hover:bg-slate-800",
                      "transition-all duration-200 ease-out",
                      "hover:scale-105 active:scale-95"
                    )}
                  >
                    <div className="w-8 h-8 rounded-full overflow-hidden">
                      {userInfo.avatar ? (
                        <Image
                          src={userInfo.avatar}
                          alt={userInfo.name}
                          width={32}
                          height={32}
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                      )}
                    </div>
                    <span className="text-sm font-medium">{userInfo.name}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      الملف الشخصي
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <LogOut className="w-4 h-4" />
                    تسجيل الخروج
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Sign In Button (when not authenticated) */}
            {!showUserMenu && variant === "default" && (
              <Button asChild variant="outline" size="sm" className="hidden md:flex">
                <Link href="/signin">تسجيل الدخول</Link>
              </Button>
            )}

            {/* Mobile Menu */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "md:hidden",
                    "transition-all duration-200 ease-out",
                    "hover:scale-105 active:scale-95"
                  )}
                >
                  {isMobileMenuOpen ? (
                    <X className="w-5 h-5" />
                  ) : (
                    <Menu className="w-5 h-5" />
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <div className="flex flex-col gap-6 pt-6">
                  {/* User Info (Mobile) */}
                  {showUserMenu && userInfo && (
                    <div className="flex items-center gap-3 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <div className="w-12 h-12 rounded-full overflow-hidden">
                        {userInfo.avatar ? (
                          <Image
                            src={userInfo.avatar}
                            alt={userInfo.name}
                            width={48}
                            height={48}
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <User className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-semibold text-slate-900 dark:text-white">
                          {userInfo.name}
                        </p>
                        {userInfo.email && (
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {userInfo.email}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Navigation Items (Mobile) */}
                  <nav className="flex flex-col gap-2">
                    {currentNavItems.map((item) => (
                      <Link
                        key={item.href}
                        href={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={cn(
                          "flex items-center gap-3 px-4 py-3 rounded-lg",
                          "transition-all duration-200 ease-out",
                          "hover:bg-slate-100 dark:hover:bg-slate-800",
                          item.isActive
                            ? "text-blue-600 dark:text-blue-400 font-semibold bg-blue-50 dark:bg-blue-900/20"
                            : "text-slate-600 dark:text-slate-400"
                        )}
                      >
                        {item.icon}
                        {item.label}
                      </Link>
                    ))}
                  </nav>

                  {/* Mobile Actions */}
                  <div className="flex flex-col gap-2 pt-4 border-t border-slate-200 dark:border-slate-700">
                    {showUserMenu ? (
                      <>
                        <Button asChild variant="outline" className="justify-start">
                          <Link href="/profile" onClick={() => setIsMobileMenuOpen(false)}>
                            <User className="w-4 h-4 mr-2" />
                            الملف الشخصي
                          </Link>
                        </Button>
                        <Button variant="outline" className="justify-start text-red-600 dark:text-red-400">
                          <LogOut className="w-4 h-4 mr-2" />
                          تسجيل الخروج
                        </Button>
                      </>
                    ) : (
                      <Button asChild variant="outline" className="justify-start">
                        <Link href="/signin" onClick={() => setIsMobileMenuOpen(false)}>
                          تسجيل الدخول
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  )
}
