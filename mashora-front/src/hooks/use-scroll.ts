"use client"

import { useEffect, useState } from "react"

interface ScrollState {
  scrollY: number
  scrollDirection: "up" | "down" | null
  isScrolled: boolean
  isScrolledPast: (threshold: number) => boolean
}

export function useScroll(): ScrollState {
  const [scrollY, setScrollY] = useState(0)
  const [scrollDirection, setScrollDirection] = useState<"up" | "down" | null>(null)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    let lastScrollY = window.scrollY
    let ticking = false

    const updateScrollState = () => {
      const currentScrollY = window.scrollY

      // Determine scroll direction
      if (currentScrollY > lastScrollY) {
        setScrollDirection("down")
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection("up")
      }

      // Update scroll position
      setScrollY(currentScrollY)
      setIsScrolled(currentScrollY > 0)

      lastScrollY = currentScrollY
      ticking = false
    }

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollState)
        ticking = true
      }
    }

    // Set initial state
    setScrollY(window.scrollY)
    setIsScrolled(window.scrollY > 0)

    window.addEventListener("scroll", handleScroll, { passive: true })

    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  const isScrolledPast = (threshold: number) => scrollY > threshold

  return {
    scrollY,
    scrollDirection,
    isScrolled,
    isScrolledPast,
  }
}
